import { useState } from 'react'
import TypewriterText from '@/components/TypewriterText'
import ChatBubble from './ChatBubble'
import ThinkingBubble from './ThinkingBubble'
import { completionTexts, thinkingTexts } from './questionData'

interface ThinkingCompleteStageProps {
  onEnterChat: () => void
}

const ThinkingCompleteStage = ({ onEnterChat }: ThinkingCompleteStageProps) => {
  const [currentThinkingIndex, setCurrentThinkingIndex] = useState(0)
  const [allThinkingComplete, setAllThinkingComplete] = useState(false)
  const [showCollapsed, setShowCollapsed] = useState(false)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [showButton, setShowButton] = useState(false)
  const [showCompletionTexts, setShowCompletionTexts] = useState(false)
  const [thinkingStartTime] = useState(Date.now())
  const [thinkingDuration, setThinkingDuration] = useState(0)

  const handleThinkingComplete = () => {
    if (currentThinkingIndex < thinkingTexts.length - 1) {
      setTimeout(() => {
        setCurrentThinkingIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有思考文字显示完成，计算用时
      const duration = Math.round((Date.now() - thinkingStartTime) / 1000)
      setThinkingDuration(duration)

      setTimeout(() => {
        setAllThinkingComplete(true)
        // 再延迟一下显示折叠效果
        setTimeout(() => {
          setShowCollapsed(true)
          // 折叠完成后显示完成文字
          setTimeout(() => {
            setShowCompletionTexts(true)
          }, 500)
        }, 1500)
      }, 1000)
    }
  }

  const handleThinkingBubbleClick = () => {
    // 点击事件留空，后续处理
  }

  const handleCompletionTextComplete = () => {
    if (currentTextIndex < completionTexts.length - 1) {
      setTimeout(() => {
        setCurrentTextIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有文字显示完成，显示按钮
      setTimeout(() => {
        setShowButton(true)
      }, 1500)
    }
  }

  return (
    <div className='flex w-full flex-col space-y-4'>
      {/* 思考气泡 - 按段落输出，下一段覆盖前一段 */}
      <ThinkingBubble
        isCollapsed={showCollapsed}
        title={
          showCollapsed
            ? `思考完毕（用时 ${thinkingDuration} 秒）`
            : '主理人正在思考'
        }
        onClick={handleThinkingBubbleClick}
      >
        {/* 只显示当前段落 */}
        {currentThinkingIndex < thinkingTexts.length && (
          <TypewriterText
            text={thinkingTexts[currentThinkingIndex]}
            speed={40}
            onComplete={handleThinkingComplete}
            className='text-base text-gray-700'
          />
        )}

        {/* 思考完成提示 */}
        {allThinkingComplete && !showCollapsed && (
          <div className='mt-3 text-sm text-gray-500'>
            思考完成，正在为您准备...
          </div>
        )}
      </ThinkingBubble>

      {/* 完成文字 */}
      {showCompletionTexts && (
        <div className='min-h-[150px] space-y-4'>
          {completionTexts.map((text, index) => (
            <div key={index}>
              {index <= currentTextIndex && (
                <ChatBubble>
                  <TypewriterText
                    text={text}
                    speed={50}
                    onComplete={
                      index === currentTextIndex
                        ? handleCompletionTextComplete
                        : undefined
                    }
                    className='text-base text-gray-700'
                  />
                </ChatBubble>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 进入聊天按钮 */}
      {showButton && (
        <div className='flex justify-center'>
          <button
            onClick={onEnterChat}
            className='animate-fade-in mt-8 rounded-full px-8 py-4 text-xl font-bold text-gray-800 shadow-md transition-all duration-300 hover:opacity-80'
            style={{
              animation: 'fadeIn 0.5s ease-in-out forwards',
              backgroundColor: '#F6F4EE',
            }}
          >
            遇见您独属的 Tina →
          </button>
        </div>
      )}
    </div>
  )
}

export default ThinkingCompleteStage
