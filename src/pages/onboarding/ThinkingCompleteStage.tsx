import { useState } from 'react'
import TypewriterText from '@/components/TypewriterText'
import ChatBubble from './ChatBubble'
import { completionTexts, thinkingTexts } from './questionData'

interface ThinkingCompleteStageProps {
  onEnterChat: () => void
}

const ThinkingCompleteStage = ({ onEnterChat }: ThinkingCompleteStageProps) => {
  const [currentThinkingIndex, setCurrentThinkingIndex] = useState(0)
  const [allThinkingComplete, setAllThinkingComplete] = useState(false)
  const [isThinkingCollapsed, setIsThinkingCollapsed] = useState(false)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [showButton, setShowButton] = useState(false)
  const [showCompletionTexts, setShowCompletionTexts] = useState(true) // 立即显示第一条消息
  const [thinkingStartTime] = useState(Date.now())
  const [thinkingDuration, setThinkingDuration] = useState(0)

  const handleThinkingComplete = () => {
    if (currentThinkingIndex < thinkingTexts.length - 1) {
      setTimeout(() => {
        setCurrentThinkingIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有思考文字显示完成，计算用时
      const duration = Math.round((Date.now() - thinkingStartTime) / 1000)
      setThinkingDuration(duration)

      setTimeout(() => {
        setAllThinkingComplete(true)
        // 再延迟一下显示折叠效果
        setTimeout(() => {
          setIsThinkingCollapsed(true)
          // 折叠完成后显示完成文字
          setTimeout(() => {
            setShowCompletionTexts(true)
          }, 500)
        }, 1500)
      }, 1000)
    }
  }

  const handleThinkingBubbleClick = () => {
    // 点击事件留空，后续处理
  }

  const handleCompletionTextComplete = () => {
    if (currentTextIndex < completionTexts.length - 1) {
      setTimeout(() => {
        setCurrentTextIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有文字显示完成，显示按钮
      setTimeout(() => {
        setShowButton(true)
      }, 1500)
    }
  }

  // 检查是否应该显示第一条消息的文字内容
  const shouldShowFirstMessageText = () => {
    return isThinkingCollapsed
  }

  // 渲染思考内容
  const renderThinkingContent = () => {
    // 如果思考已完成且已折叠，不显示内容
    if (allThinkingComplete && isThinkingCollapsed) return null

    return (
      <div>
        {/* 只显示当前段落 */}
        {currentThinkingIndex < thinkingTexts.length && (
          <TypewriterText
            text={thinkingTexts[currentThinkingIndex]}
            speed={40}
            onComplete={handleThinkingComplete}
            className='text-base text-gray-700'
          />
        )}

        {/* 思考完成提示 */}
        {allThinkingComplete && !isThinkingCollapsed && (
          <div className='mt-3 text-sm text-gray-500'>
            思考完成，正在为您准备...
          </div>
        )}
      </div>
    )
  }

  return (
    <div className='flex w-full flex-col space-y-4'>
      {/* 完成文字 - 第一条包含思考内容 */}
      {showCompletionTexts && (
        <div className='min-h-[150px] space-y-4'>
          {completionTexts.map((text, index) => (
            <div key={index}>
              {index <= currentTextIndex && (
                <ChatBubble
                  thinkContent={
                    index === 0 ? renderThinkingContent() : undefined
                  }
                  isThinkingCollapsed={
                    index === 0 ? isThinkingCollapsed : undefined
                  }
                  thinkingTitle={
                    index === 0
                      ? isThinkingCollapsed
                        ? `思考完毕（用时 ${thinkingDuration} 秒）`
                        : '主理人正在思考'
                      : undefined
                  }
                  onThinkingClick={
                    index === 0 ? handleThinkingBubbleClick : undefined
                  }
                >
                  {/* 第一条消息的文字内容只在思考完成并折叠后才显示 */}
                  {(index === 0 && shouldShowFirstMessageText()) ||
                  index > 0 ? (
                    <TypewriterText
                      text={text}
                      speed={50}
                      onComplete={
                        index === currentTextIndex
                          ? handleCompletionTextComplete
                          : undefined
                      }
                      className='text-base text-gray-700'
                    />
                  ) : (
                    <div className='h-0'></div> // 占位符，保持气泡结构
                  )}
                </ChatBubble>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 进入聊天按钮 */}
      {showButton && (
        <div className='flex justify-center'>
          <button
            onClick={onEnterChat}
            className='animate-fade-in mt-8 rounded-full px-8 py-4 text-xl font-bold text-gray-800 shadow-md transition-all duration-300 hover:opacity-80'
            style={{
              animation: 'fadeIn 0.5s ease-in-out forwards',
              backgroundColor: '#F6F4EE',
            }}
          >
            遇见您独属的 Tina →
          </button>
        </div>
      )}
    </div>
  )
}

export default ThinkingCompleteStage
