import { useState } from 'react'
import TypewriterText from '@/components/TypewriterText'
import ChatBubble from './ChatBubble'
import { completionTexts, thinkingTexts } from './questionData'

interface ThinkingCompleteStageProps {
  onEnterChat: () => void
}

const ThinkingCompleteStage = ({ onEnterChat }: ThinkingCompleteStageProps) => {
  const [currentThinkingIndex, setCurrentThinkingIndex] = useState(0)
  const [isThinkingCollapsed, setIsThinkingCollapsed] = useState(false)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [showButton, setShowButton] = useState(false)
  const [thinkingStartTime] = useState(Date.now())
  const [thinkingDuration, setThinkingDuration] = useState(0)

  const isThinkingComplete = currentThinkingIndex >= thinkingTexts.length

  const handleThinkingComplete = () => {
    if (currentThinkingIndex < thinkingTexts.length - 1) {
      setTimeout(() => setCurrentThinkingIndex((prev) => prev + 1), 1000)
    } else {
      const duration = Math.round((Date.now() - thinkingStartTime) / 1000)
      setThinkingDuration(duration)

      setTimeout(() => {
        setTimeout(() => setIsThinkingCollapsed(true), 1500)
      }, 1000)
    }
  }

  const handleCompletionTextComplete = () => {
    if (currentTextIndex < completionTexts.length - 1) {
      setTimeout(() => setCurrentTextIndex((prev) => prev + 1), 1000)
    } else {
      setTimeout(() => setShowButton(true), 1500)
    }
  }

  const handleThinkingClick = () => {
    // 点击事件留空，后续处理
  }

  // 思考数据
  const thinkingData = {
    content: (
      <>
        {currentThinkingIndex < thinkingTexts.length && (
          <TypewriterText
            text={thinkingTexts[currentThinkingIndex]}
            speed={40}
            onComplete={handleThinkingComplete}
            className='text-base text-gray-700'
          />
        )}
        {isThinkingComplete && !isThinkingCollapsed && (
          <div className='mt-3 text-sm text-gray-500'>
            思考完成，正在为您准备...
          </div>
        )}
      </>
    ),
    isCollapsed: isThinkingCollapsed,
    title: isThinkingCollapsed
      ? `思考完毕（用时 ${thinkingDuration} 秒）`
      : '主理人正在思考',
    onClick: handleThinkingClick,
  }

  return (
    <div className='flex w-full flex-col space-y-4'>
      <div className='min-h-[150px] space-y-4'>
        {completionTexts.map((text, index) => (
          <div key={index}>
            {index <= currentTextIndex && (
              <ChatBubble thinking={index === 0 ? thinkingData : undefined}>
                {/* 第一条消息的文字内容只在思考完成并折叠后才显示 */}
                {(index === 0 && isThinkingCollapsed) || index > 0 ? (
                  <TypewriterText
                    text={text}
                    speed={50}
                    onComplete={
                      index === currentTextIndex
                        ? handleCompletionTextComplete
                        : undefined
                    }
                    className='text-base text-gray-700'
                  />
                ) : (
                  <div className='h-0' />
                )}
              </ChatBubble>
            )}
          </div>
        ))}
      </div>

      {/* 进入聊天按钮 */}
      {showButton && (
        <div className='flex justify-center'>
          <button
            onClick={onEnterChat}
            className='animate-fade-in mt-8 rounded-full px-8 py-4 text-xl font-bold text-gray-800 shadow-md transition-all duration-300 hover:opacity-80'
            style={{
              animation: 'fadeIn 0.5s ease-in-out forwards',
              backgroundColor: '#F6F4EE',
            }}
          >
            遇见您独属的 Tina →
          </button>
        </div>
      )}
    </div>
  )
}

export default ThinkingCompleteStage
