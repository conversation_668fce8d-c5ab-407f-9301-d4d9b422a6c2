// 问答数据结构定义

export interface QuestionOption {
  id: string
  text: string
  emoji?: string // 可选的表情符号
}

export interface Question {
  id: string
  text: string
  options: QuestionOption[]
}

export interface AIResponse {
  summary: string // 对用户回答的总结
  nextIntroduction: string // 下一个问题的引导语
}

// Welcome 文本
export const welcomeTexts = [
  '♪ 滴—答♪ 初次见面！欢迎来到「私人助理」俱乐部',
  '仅需 1分钟 完成 4道选择题',
  '系统就能自动锁定最适合您的助理类型！',
]

// 静态问题数据
export const onboardingQuestions: Question[] = [
  {
    id: 'age',
    text: '1. 您属于哪个年龄区间呢？',
    options: [
      { id: 'age_10_17', text: '10 - 17 岁' },
      { id: 'age_18_35', text: '18 - 35 岁' },
      { id: 'age_36_55', text: '36 - 55 岁' },
      { id: 'age_55_plus', text: '55 岁以上' },
      { id: 'age_skip', text: '稍后再说' },
    ],
  },
  {
    id: 'lifestyle',
    text: '2. 您最近处于哪种生活节奏呢？',
    options: [
      { id: 'lifestyle_study', text: '🏔️ 求学阶段', emoji: '🏔️' },
      { id: 'lifestyle_work', text: '🎭 职场冲刺', emoji: '🎭' },
      { id: 'lifestyle_job_hunting', text: '⚡ 职场法师', emoji: '⚡' },
      { id: 'lifestyle_creative', text: '👑 灵活创收', emoji: '👑' },
      { id: 'lifestyle_family', text: '👨‍👩‍👧‍👦 家庭时光', emoji: '👨‍👩‍👧‍👦' },
      { id: 'lifestyle_leisure', text: '🌅 休闲探索', emoji: '🌅' },
      { id: 'lifestyle_skip', text: '稍后再说' },
    ],
  },
  {
    id: 'interests',
    text: '3. 您平时最感兴趣的话题是？',
    options: [
      { id: 'interests_tech', text: '💻 科技数码' },
      { id: 'interests_finance', text: '💰 理财投资' },
      { id: 'interests_health', text: '🏃‍♂️ 健康养生' },
      { id: 'interests_travel', text: '✈️ 旅行探索' },
      { id: 'interests_food', text: '🍜 美食烹饪' },
      { id: 'interests_entertainment', text: '🎬 娱乐八卦' },
      { id: 'interests_skip', text: '稍后再说' },
    ],
  },
  {
    id: 'communication_style',
    text: '4. 您希望助理用什么风格和您交流？',
    options: [
      { id: 'style_professional', text: '🤵 专业严谨' },
      { id: 'style_friendly', text: '😊 亲切友好' },
      { id: 'style_humorous', text: '😄 幽默风趣' },
      { id: 'style_concise', text: '⚡ 简洁高效' },
      { id: 'style_detailed', text: '📝 详细周到' },
      { id: 'style_creative', text: '🎨 创意灵感' },
      { id: 'style_skip', text: '稍后再说' },
    ],
  },
]

// 模拟 AI 回复数据
export const mockAIResponses: Record<string, AIResponse> = {
  // 年龄相关回复
  age_10_17: {
    summary: '了解到您正值青春年华，充满活力和好奇心！',
    nextIntroduction: '接下来让我了解一下您的生活状态～',
  },
  age_18_35: {
    summary: '您正处在人生的黄金时期，事业和生活都充满可能性！',
    nextIntroduction: '让我们继续了解您的生活节奏～',
  },
  age_36_55: {
    summary: '您正值人生的成熟阶段，经验丰富且目标明确！',
    nextIntroduction: '想了解一下您目前的生活状态～',
  },
  age_55_plus: {
    summary: '您拥有丰富的人生阅历，是智慧的象征！',
    nextIntroduction: '让我了解一下您的生活节奏～',
  },
  age_skip: {
    summary: '没关系，我们可以通过其他方式了解您～',
    nextIntroduction: '让我们聊聊您的生活状态吧～',
  },

  // 生活节奏相关回复
  lifestyle_study: {
    summary: '求学路上，每一步都是成长的足迹！',
    nextIntroduction: '想了解一下您平时关注什么话题～',
  },
  lifestyle_work: {
    summary: '职场打拼，为梦想努力奋斗的您真棒！',
    nextIntroduction: '让我了解一下您的兴趣爱好～',
  },
  lifestyle_job_hunting: {
    summary: '职场转换期，相信您一定能找到理想的工作！',
    nextIntroduction: '想知道您平时关注哪些话题～',
  },
  lifestyle_creative: {
    summary: '灵活创收，您一定是个很有想法的人！',
    nextIntroduction: '让我了解一下您的兴趣领域～',
  },
  lifestyle_family: {
    summary: '家庭时光珍贵，您一定是个很温暖的人！',
    nextIntroduction: '想了解一下您平时的兴趣爱好～',
  },
  lifestyle_leisure: {
    summary: '休闲探索，享受生活的您真令人羡慕！',
    nextIntroduction: '让我了解一下您感兴趣的话题～',
  },
  lifestyle_skip: {
    summary: '理解您的想法，我们继续其他话题～',
    nextIntroduction: '让我了解一下您的兴趣爱好～',
  },

  // 兴趣相关回复
  interests_tech: {
    summary: '科技数码达人！您一定对新技术很敏感～',
    nextIntroduction: '最后想了解一下您喜欢的交流风格～',
  },
  interests_finance: {
    summary: '理财投资高手！您一定很有商业头脑～',
    nextIntroduction: '最后一个问题，关于交流风格～',
  },
  interests_health: {
    summary: '健康养生专家！您很注重生活品质～',
    nextIntroduction: '最后想了解您偏好的交流方式～',
  },
  interests_travel: {
    summary: '旅行探索家！您一定有很多精彩的经历～',
    nextIntroduction: '最后一个问题，关于沟通风格～',
  },
  interests_food: {
    summary: '美食烹饪爱好者！您一定很会享受生活～',
    nextIntroduction: '最后想了解您的交流偏好～',
  },
  interests_entertainment: {
    summary: '娱乐达人！您一定很了解流行趋势～',
    nextIntroduction: '最后一个问题，关于交流风格～',
  },
  interests_skip: {
    summary: '没关系，每个人都有自己的节奏～',
    nextIntroduction: '最后想了解一下您的交流偏好～',
  },

  // 交流风格相关回复
  style_professional: {
    summary: '专业严谨的风格，我会用正式的语言与您交流！',
    nextIntroduction: '太好了！现在我对您有了初步了解～',
  },
  style_friendly: {
    summary: '亲切友好的风格，我们会像朋友一样聊天！',
    nextIntroduction: '完美！现在我知道怎么和您交流了～',
  },
  style_humorous: {
    summary: '幽默风趣的风格，我们的对话会很有趣！',
    nextIntroduction: '哈哈，我们一定会聊得很开心～',
  },
  style_concise: {
    summary: '简洁高效的风格，我会直接给您最有用的信息！',
    nextIntroduction: '明白了！我会简洁明了地回复您～',
  },
  style_detailed: {
    summary: '详细周到的风格，我会给您全面的信息和建议！',
    nextIntroduction: '好的！我会详细地为您解答问题～',
  },
  style_creative: {
    summary: '创意灵感的风格，我们的对话会充满想象力！',
    nextIntroduction: '太棒了！我们会有很多创意的交流～',
  },
  style_skip: {
    summary: '没关系，我会根据情况调整交流方式～',
    nextIntroduction: '好的！我会灵活地与您交流～',
  },
}

// 思考过程文本
export const thinkingTexts = [
  '由于用户说明了表达了自己的年龄段、现状感想、兴趣领域、以及沟通风格偏好。看起来 ta 是想要找到最懂 ta 的私人助理。',
  `正在分析您的回答...
  根据您的选择，我发现您是一个很有趣的人,
  让我为您匹配最合适的助理风格,
  正在生成个性化的交流方案...
  几乎完成了，马上就好！`,
]

// 完成引导的欢迎文字
export const completionTexts = [
  '太棒了！我已经了解了您的基本情况',
  '基于您的回答，我会用最适合您的方式与您交流',
  '现在，让我们开始愉快的对话吧！',
]
