import { useState } from 'react'
import robotImage from '@/assets/robot.png'
import TypewriterText from '@/components/TypewriterText'

// 统一的消息数据结构
export interface MessageData {
  text: string
  think?: {
    segments: string[]
    startTime?: number
  }
}

interface ChatBubbleProps {
  message: MessageData
  onThinkingComplete?: (duration: number) => void
  onTextComplete?: () => void
  showText?: boolean // 控制是否显示正文内容
}

const ChatBubble = ({
  message,
  onThinkingComplete,
  onTextComplete,
  showText = true,
}: ChatBubbleProps) => {
  // Think 相关状态 - 内聚到 ChatBubble 内部
  const [currentThinkIndex, setCurrentThinkIndex] = useState(0)
  const [isThinkingCollapsed, setIsThinkingCollapsed] = useState(false)
  const [thinkingDuration, setThinkingDuration] = useState(0)
  const [thinkStartTime] = useState(message.think?.startTime || Date.now())

  const bubbleClasses =
    'relative max-w-[85%] break-words rounded-lg border border-gray-100 bg-white shadow-sm'
  const arrowClasses =
    'absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white'

  const hasThinking = message.think && message.think.segments.length > 0
  const isThinkingComplete =
    currentThinkIndex >= (message.think?.segments.length || 0)

  const handleThinkSegmentComplete = () => {
    if (!message.think) return

    if (currentThinkIndex < message.think.segments.length - 1) {
      setTimeout(() => setCurrentThinkIndex((prev) => prev + 1), 1000)
    } else {
      // 思考完成，立即停止计时
      const duration = Math.round((Date.now() - thinkStartTime) / 1000)
      setThinkingDuration(duration)

      setTimeout(() => {
        setTimeout(() => {
          setIsThinkingCollapsed(true)
          onThinkingComplete?.(duration)
        }, 1500)
      }, 1000)
    }
  }

  const handleThinkingClick = () => {
    // 点击事件留空，后续处理
  }

  const getThinkingTitle = () => {
    if (!hasThinking) return ''
    return isThinkingCollapsed
      ? `思考完毕（用时 ${thinkingDuration} 秒）`
      : '主理人正在思考'
  }

  // 判断是否应该显示文字内容
  const shouldShowTextBubble = showText && message.text.trim()

  return (
    <div className='friend group flex flex-col space-y-4'>
      <div className='relative flex max-w-[85%] space-x-3'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-10 w-10 min-w-10 cursor-pointer rounded object-cover object-center'
        />

        <div className='flex flex-col'>
          {/* 思考部分 - 标题向上偏移，不与头像对齐 */}
          {hasThinking && (
            <div className='-mt-1 mb-2'>
              <div className='mb-1 text-sm font-medium text-gray-600'>
                {getThinkingTitle()}
              </div>
              <div
                className={`${bubbleClasses} transition-all duration-1000 ${
                  isThinkingCollapsed
                    ? 'cursor-pointer p-2 hover:bg-gray-50'
                    : 'p-3'
                }`}
                onClick={isThinkingCollapsed ? handleThinkingClick : undefined}
              >
                <div className={arrowClasses} />
                <div
                  className={`relative z-10 transition-all duration-500 ${
                    isThinkingCollapsed
                      ? 'h-0 overflow-hidden opacity-0'
                      : 'opacity-100'
                  }`}
                >
                  {/* 当前思考段落 */}
                  {currentThinkIndex <
                    (message.think?.segments.length || 0) && (
                    <TypewriterText
                      text={message.think!.segments[currentThinkIndex]}
                      speed={40}
                      onComplete={handleThinkSegmentComplete}
                      className='text-base text-gray-700'
                    />
                  )}

                  {/* 思考完成提示 */}
                  {isThinkingComplete && !isThinkingCollapsed && (
                    <div className='mt-3 text-sm text-gray-500'>
                      思考完成，正在为您准备...
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 消息气泡 - 严格控制显示条件 */}
          {shouldShowTextBubble && (
            <div className={`${bubbleClasses} p-3`}>
              <div className={arrowClasses} />
              <div className='relative z-10'>
                <TypewriterText
                  text={message.text}
                  speed={50}
                  onComplete={onTextComplete}
                  className='text-base text-gray-700'
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChatBubble
