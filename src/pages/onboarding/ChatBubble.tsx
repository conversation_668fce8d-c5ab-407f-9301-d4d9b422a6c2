import { ReactNode } from 'react'
import robotImage from '@/assets/robot.png'

interface ChatBubbleProps {
  children: ReactNode
}

const ChatBubble = ({ children }: ChatBubbleProps) => {
  return (
    <div className='friend group flex flex-col space-y-4'>
      <div className='relative flex max-w-[85%] space-x-3'>
        {/* 机器人头像 */}
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-10 w-10 min-w-10 cursor-pointer rounded object-cover object-center'
        />

        {/* 消息气泡 */}
        <div className='relative max-w-[85%] break-words rounded-lg border border-gray-100 bg-white p-3 shadow-sm'>
          {/* 气泡尖角 */}
          <div className='absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white' />
          <div className='relative z-10'>{children}</div>
        </div>
      </div>
    </div>
  )
}

export default ChatBubble
