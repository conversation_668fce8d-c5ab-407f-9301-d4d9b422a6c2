import { ReactNode } from 'react'
import robotImage from '@/assets/robot.png'

interface ChatBubbleProps {
  children: ReactNode
  thinkContent?: ReactNode
  isThinkingCollapsed?: boolean
  thinkingTitle?: string
  onThinkingClick?: () => void
}

const ChatBubble = ({
  children,
  thinkContent,
  isThinkingCollapsed = false,
  thinkingTitle = '主理人正在思考',
  onThinkingClick,
}: ChatBubbleProps) => {
  return (
    <div className='friend group flex flex-col space-y-4'>
      <div className='relative flex max-w-[85%] space-x-3'>
        {/* 机器人头像 */}
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-10 w-10 min-w-10 cursor-pointer rounded object-cover object-center'
        />

        <div className='flex flex-col space-y-2'>
          {/* 思考部分 - 标题在气泡外 */}
          {thinkContent && (
            <div className='flex flex-col'>
              {/* 思考标题 */}
              <div className='mb-1 text-sm font-medium text-gray-600'>
                {thinkingTitle}
              </div>

              {/* 思考气泡 */}
              <div
                className={`relative max-w-[85%] break-words rounded-lg border border-gray-100 bg-white shadow-sm transition-all duration-1000 ${
                  isThinkingCollapsed
                    ? 'cursor-pointer p-2 hover:bg-gray-50'
                    : 'p-3'
                }`}
                onClick={isThinkingCollapsed ? onThinkingClick : undefined}
              >
                {/* 气泡尖角 */}
                <div className='absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white' />

                {/* 思考内容 */}
                <div
                  className={`relative z-10 transition-all duration-500 ${
                    isThinkingCollapsed
                      ? 'h-0 overflow-hidden opacity-0'
                      : 'opacity-100'
                  }`}
                >
                  {thinkContent}
                </div>
              </div>
            </div>
          )}

          {/* 普通消息气泡 */}
          <div className='relative max-w-[85%] break-words rounded-lg border border-gray-100 bg-white p-3 shadow-sm'>
            {/* 气泡尖角 */}
            <div className='absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white' />
            <div className='relative z-10'>{children}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatBubble
