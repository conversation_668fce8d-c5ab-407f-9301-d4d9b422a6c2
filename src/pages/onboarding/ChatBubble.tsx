import { ReactNode } from 'react'
import robotImage from '@/assets/robot.png'

interface ChatBubbleProps {
  children: ReactNode
  thinking?: {
    content: ReactNode
    isCollapsed: boolean
    title: string
    onClick?: () => void
  }
}

const ChatBubble = ({ children, thinking }: ChatBubbleProps) => {
  const bubbleClasses =
    'relative max-w-[85%] break-words rounded-lg border border-gray-100 bg-white shadow-sm'
  const arrowClasses =
    'absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white'

  return (
    <div className='friend group flex flex-col space-y-4'>
      <div className='relative flex max-w-[85%] space-x-3'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-10 w-10 min-w-10 cursor-pointer rounded object-cover object-center'
        />

        <div className='flex flex-col space-y-2'>
          {/* 思考部分 */}
          {thinking && (
            <>
              <div className='mb-1 text-sm font-medium text-gray-600'>
                {thinking.title}
              </div>
              <div
                className={`${bubbleClasses} transition-all duration-1000 ${
                  thinking.isCollapsed
                    ? 'cursor-pointer p-2 hover:bg-gray-50'
                    : 'p-3'
                }`}
                onClick={thinking.isCollapsed ? thinking.onClick : undefined}
              >
                <div className={arrowClasses} />
                <div
                  className={`relative z-10 transition-all duration-500 ${
                    thinking.isCollapsed
                      ? 'h-0 overflow-hidden opacity-0'
                      : 'opacity-100'
                  }`}
                >
                  {thinking.content}
                </div>
              </div>
            </>
          )}

          {/* 消息气泡 */}
          <div className={`${bubbleClasses} p-3`}>
            <div className={arrowClasses} />
            <div className='relative z-10'>{children}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatBubble
