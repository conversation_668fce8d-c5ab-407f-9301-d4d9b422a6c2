import { ReactNode } from 'react'
import robotImage from '@/assets/robot.png'

interface ThinkingBubbleProps {
  children: ReactNode
  isCollapsed?: boolean
}

const ThinkingBubble = ({ children, isCollapsed = false }: ThinkingBubbleProps) => {
  return (
    <div className='group friend flex flex-col space-y-4'>
        {/* 思考气泡 */}
        <div 
          className={`relative max-w-[85%] break-words rounded-lg border border-gray-100 bg-white p-3 shadow-sm transition-all duration-1000 ${
            isCollapsed ? 'h-0 overflow-hidden opacity-0' : 'opacity-100'
          }`}
        >
          {/* 气泡尖角 */}
          <div className='absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white' />
          <div className="relative z-10 space-y-3">
            {children}
          </div>
      </div>
    </div>
  )
}

export default ThinkingBubble
