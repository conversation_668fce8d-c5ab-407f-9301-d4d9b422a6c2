import { ReactNode } from 'react'
import robotImage from '@/assets/robot.png'

interface ThinkingBubbleProps {
  children: ReactNode
  isCollapsed?: boolean
  title?: string
  onClick?: () => void
}

const ThinkingBubble = ({
  children,
  isCollapsed = false,
  title = '主理人正在思考',
  onClick,
}: ThinkingBubbleProps) => {
  return (
    <div className='friend group flex flex-col space-y-4'>
      {/* 思考气泡 */}
      <div
        className={`relative max-w-[85%] break-words rounded-lg border border-gray-100 bg-white shadow-sm transition-all duration-1000 ${
          isCollapsed ? 'cursor-pointer p-2 hover:bg-gray-50' : 'p-3'
        }`}
        onClick={isCollapsed ? onClick : undefined}
      >
        {/* 气泡尖角 */}
        <div className='absolute -left-1.5 top-3 h-3 w-3 rotate-45 border-b border-l border-gray-100 bg-white' />

        {/* 标题 */}
        <div className='relative z-10 mb-2 text-sm font-medium text-gray-600'>
          {title}
        </div>

        {/* 内容 */}
        <div
          className={`relative z-10 transition-all duration-500 ${
            isCollapsed ? 'h-0 overflow-hidden opacity-0' : 'opacity-100'
          }`}
        >
          <div className='space-y-3'>{children}</div>
        </div>
      </div>
    </div>
  )
}

export default ThinkingBubble
